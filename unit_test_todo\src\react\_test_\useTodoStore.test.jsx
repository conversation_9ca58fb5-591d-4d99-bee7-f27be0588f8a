import { act, renderHook } from "@testing-library/react"
import { describe, expect, it } from "vitest"
import { useTodoStore } from "../hooks/useTodoStore"
describe("useTodoStore", () => {
  it("初始todos为空", () => {
    const { result } = renderHook(() => useTodoStore())
    expect(result.current.todos).toEqual([])
  })

  it("addTodo能添加新项，且去除首尾空格", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("  测试  ")
    })
    expect(result.current.todos.length).toBe(1)
    expect(result.current.todos[0].text).toBe("测试")
  })

  it("addTodo传空字符串无效", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("   ")
    })
    expect(result.current.todos.length).toBe(0)
  })

  it("toggleTodo能切换完成状态", () => {
    const { result } = renderHook(() => useTodoStore())
    let id
    act(() => {
      result.current.addTodo("任务1")
    })
    id = result.current.todos[0].id
    act(() => {
      result.current.toggleTodo(id)
    })
    expect(result.current.todos[0].completed).toBe(true)
    act(() => {
      result.current.toggleTodo(id)
    })
    expect(result.current.todos[0].completed).toBe(false)
  })

  it("deleteTodo能删除指定项", () => {
    const { result } = renderHook(() => useTodoStore())
    let id
    act(() => {
      result.current.addTodo("任务1")
    })
    id = result.current.todos[0].id
    act(() => {
      result.current.deleteTodo(id)
    })
    expect(result.current.todos.length).toBe(0)
  })

  it("clearCompleted能清除所有已完成项", () => {
    const { result } = renderHook(() => useTodoStore())
    let id1, id2
    act(() => {
      result.current.addTodo("a")
    })
    id1 = result.current.todos[0].id
    act(() => {
      result.current.addTodo("b")
    })
    id2 = result.current.todos[1].id
    act(() => {
      result.current.toggleTodo(id1)
    })
    act(() => {
      result.current.clearCompleted()
    })

    expect(result.current.todos.length).toBe(1)
    expect(result.current.todos[0].id).toBe(id2)
  })

  it("updateTodo能更新指定项内容", () => {
    const { result } = renderHook(() => useTodoStore())
    let id
    act(() => {
      result.current.addTodo("old")
    })
    id = result.current.todos[0].id
    act(() => {
      result.current.updateTodo(id, { text: "new", completed: true })
    })
    expect(result.current.todos[0].text).toBe("new")
    expect(result.current.todos[0].completed).toBe(true)
  })

  it("updateTodo对不存在的ID无效果", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("original")
    })
    const originalTodo = result.current.todos[0]

    // 尝试更新不存在的ID
    act(() => {
      result.current.updateTodo(999999, { text: "changed" })
    })

    // 原todo应该保持不变
    expect(result.current.todos[0]).toEqual(originalTodo)
    expect(result.current.todos[0].text).toBe("original")
  })

  it("uncompletedCount、completedCount、totalCount统计正确", () => {
    const { result } = renderHook(() => useTodoStore())
    let id1, id2
    act(() => {
      result.current.addTodo("a")
    })
    id1 = result.current.todos[0].id
    act(() => {
      result.current.addTodo("b")
    })
    id2 = result.current.todos[1].id
    act(() => {
      result.current.toggleTodo(id1)
    })
    expect(result.current.uncompletedCount).toBe(1)
    expect(result.current.completedCount).toBe(1)
    expect(result.current.totalCount).toBe(2)
  })

  it("filteredTodos能按条件过滤", () => {
    const { result } = renderHook(() => useTodoStore())
    let id1, id2
    act(() => {
      result.current.addTodo("a")
    })
    id1 = result.current.todos[0].id
    act(() => {
      result.current.addTodo("b")
    })
    id2 = result.current.todos[1].id
    act(() => {
      result.current.toggleTodo(id1)
    })
    expect(result.current.filteredTodos("all").length).toBe(2)
    expect(result.current.filteredTodos("active").length).toBe(1)
    expect(result.current.filteredTodos("completed").length).toBe(1)
  })

  it("filteredTodos对未知过滤条件返回所有项", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("test")
    })
    // 测试默认情况（未知过滤条件）
    expect(result.current.filteredTodos("unknown").length).toBe(1)
    expect(result.current.filteredTodos().length).toBe(1)
  })

  it("toggleTodo对不存在的ID无效果", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("test")
    })
    const originalTodo = result.current.todos[0]

    // 不存在的ID
    act(() => {
      result.current.toggleTodo(999999)
    })

    // 原todo应该保持不变
    expect(result.current.todos[0]).toEqual(originalTodo)
  })

  it("deleteTodo对不存在的ID无效果", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("test")
    })
    const originalLength = result.current.todos.length

    // 尝试删除不存在的ID
    act(() => {
      result.current.deleteTodo(999999)
    })

    // todos数量应该保持不变
    expect(result.current.todos.length).toBe(originalLength)
  })

  it("clearCompleted在没有已完成项时无效果", () => {
    const { result } = renderHook(() => useTodoStore())
    act(() => {
      result.current.addTodo("active task")
    })
    const originalLength = result.current.todos.length

    // 清除已完成项（但没有已完成项）
    act(() => {
      result.current.clearCompleted()
    })

    // todos数量应该保持不变
    expect(result.current.todos.length).toBe(originalLength)
  })
})
