{"name": "todo_test", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "vite", "build": "vue-tsc && vite build", "preview": "vite preview", "type-check": "vue-tsc --noEmit", "test": "vitest", "coverage": "vitest run --coverage"}, "keywords": [], "type": "module", "author": "", "license": "ISC", "dependencies": {"@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "react": "^19.1.0", "react-dom": "^19.1.0", "vue": "^3.5.17"}, "devDependencies": {"@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.3.0", "@types/node": "^20.4.5", "@vitejs/plugin-react": "^4.6.0", "@vitejs/plugin-vue": "^6.0.0", "@vitest/coverage-v8": "3.2.4", "jsdom": "^26.1.0", "typescript": "^5.1.6", "vite": "^5.4.19", "vitest": "^3.2.4", "vitest-dom": "^0.1.1"}}