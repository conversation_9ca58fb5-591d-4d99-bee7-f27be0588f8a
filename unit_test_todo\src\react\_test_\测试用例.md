先写测试用例（用中文），然后根据测试用例再写单测代码

## 组件测试用例

### TodoApp

1. 应该渲染输入框、统计栏和过滤按钮
2. 添加新待办项后，列表应增加，并显示日期
3. 过滤按钮切换，显示不同的待办项
4. 清除已完成按钮功能
5. 删除、切换、编辑待办项功能
6. 空列表时显示正确的空状态提示

### TodoInput 组件测试用例

- 渲染测试
  应该渲染一个输入框和一个按钮。
- 输入与按钮状态
  输入有效内容时，按钮应可点击。
  输入无效内容（如全空格或空字符串）时，按钮应禁用。
- 提交行为
  输入有效内容并点击按钮，应调用 onSubmit 回调，并清空输入框。
  输入有效内容并按 Enter 键，应调用 onSubmit 回调，并清空输入框。
- 取消行为
  输入内容后按 ESC 键，应调用 onCancel 回调，并清空输入框。
- 自定义属性
  可以自定义按钮文本和输入框 placeholder。
- 最大长度限制
  输入内容超过 maxLength 时，输入框内容应被截断。

### TodoItem 组件测试用例

- 应该正常渲染内容
- 勾选切换完成状态，触发 onToggle
- 点击删除按钮触发 onDelete
- 点击编辑按钮触发 onEdit
- showDate 为 true 时展示日期
- 已完成时有 completed 样式
- 删除/编辑按钮阻止事件冒泡

### TodoStats 组件测试用例

- 正常渲染统计信息
- stats 为 null 时显示默认值
- 完成率仅在 total>0 时显示
- 清除已完成按钮可用/禁用状态
- 点击清除已完成按钮触发 onClearCompleted

## utils 工具函数测试用例【todoUtils.js 测试】

- 传入合法 Date 对象，返回格式化字符串。
- 传入 null、undefined，返回空字符串。
- 传入非法日期字符串或无效 Date，返回空字符串。
- 传入时间戳（number），返回格式化字符串。
- 传入空字符串，返回空字符串。

## hooks 测试用例【useTodoStore.js】

- 边界情况测试
