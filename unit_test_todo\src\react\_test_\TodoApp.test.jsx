import { fireEvent, render, screen } from "@testing-library/react"
import { describe, expect, it } from "vitest"

import React from "react"
import TodoApp from "../TodoApp"

describe("TodoApp 组件", () => {
  it("应该渲染输入框、统计栏和过滤按钮", () => {
    render(<TodoApp />)
    expect(screen.getByRole("textbox")).toBeInTheDocument()
    expect(screen.getByText("清除已完成")).toBeInTheDocument()
    expect(screen.getByText("全部")).toBeInTheDocument()
    expect(screen.getByText("进行中")).toBeInTheDocument()
    expect(screen.getByText("已完成")).toBeInTheDocument()
  })

  it("添加新待办项后，列表应增加，并显示日期", () => {
    render(<TodoApp />)
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button", { name: /add/i })
    fireEvent.change(input, { target: { value: "新任务" } })
    fireEvent.click(button)
    expect(screen.getByText("新任务")).toBeInTheDocument()
    // 检查日期（年份4位数字）
    expect(screen.getByText(/\d{4}/)).toBeInTheDocument()
  })

  // it("切换过滤按钮，显示不同的待办项", () => {
  //   render(<TodoApp />)
  //   const input = screen.getByRole("textbox")
  //   const addButton = screen.getByRole("button", { name: /add/i })
  //   fireEvent.change(input, { target: { value: "任务1" } })
  //   fireEvent.click(addButton)
  //   fireEvent.change(input, { target: { value: "任务2" } })
  //   fireEvent.click(addButton)
  //   // 找到“任务1”对应的复选框并点击
  //   const task1Checkbox = screen.getByLabelText("任务1") || screen.getAllByRole("checkbox")[1]
  //   fireEvent.click(task1Checkbox)
  //   // 切换到“已完成”
  //   fireEvent.click(screen.getByText("已完成"))
  //   expect(screen.getByText("任务1")).toBeInTheDocument()
  //   expect(screen.queryByText("任务2")).not.toBeInTheDocument()
  //   // 切换到“进行中”
  //   fireEvent.click(screen.getByText("进行中"))
  //   expect(screen.getByText("任务2")).toBeInTheDocument()
  //   expect(screen.queryByText("任务1")).not.toBeInTheDocument()
  // })

  it("点击清除已完成按钮，已完成项应被移除", () => {
    render(<TodoApp />)
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button", { name: /add/i })
    fireEvent.change(input, { target: { value: "任务1" } })
    fireEvent.click(button)
    fireEvent.change(input, { target: { value: "任务2" } })
    fireEvent.click(button)
    // 完成任务1
    const checkbox = screen.getAllByRole("checkbox")[0]
    fireEvent.click(checkbox)
    // 清除已完成
    fireEvent.click(screen.getByText("清除已完成"))
    expect(screen.queryByText("任务1")).not.toBeInTheDocument()
    expect(screen.getByText("任务2")).toBeInTheDocument()
  })

  it("删除、切换、编辑待办项功能", () => {
    render(<TodoApp />)
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button", { name: /add/i })
    fireEvent.change(input, { target: { value: "任务1" } })
    fireEvent.click(button)
    // 删除
    const delBtn = screen.getByTitle("删除")
    fireEvent.click(delBtn)
    expect(screen.queryByText("任务1")).not.toBeInTheDocument()
    // 编辑（这里只能触发回调，实际编辑功能未实现）
    fireEvent.change(input, { target: { value: "任务2" } })
    fireEvent.click(button)
    const editBtn = screen.getByTitle("编辑")
    fireEvent.click(editBtn)
    // 只要不报错即可
  })

  it("空列表时显示正确的空状态提示", () => {
    render(<TodoApp />)
    expect(screen.getByText("还没有待办项，开始添加吧！")).toBeInTheDocument()
    // 添加并完成任务
    const input = screen.getByRole("textbox")
    const button = screen.getByRole("button", { name: /add/i })
    fireEvent.change(input, { target: { value: "任务1" } })
    fireEvent.click(button)
    const checkbox = screen.getByRole("checkbox")
    fireEvent.click(checkbox)
    // 切换到“进行中”
    fireEvent.click(screen.getByText("进行中"))
    expect(screen.getByText("没有进行中的待办项")).toBeInTheDocument()
    // 切换到“已完成”
    fireEvent.click(screen.getByText("已完成"))
    expect(screen.queryByText("没有已完成的待办项")).not.toBeNull()
  })
})