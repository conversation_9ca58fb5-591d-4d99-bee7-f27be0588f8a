<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>practice 4</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      body {
        background-color: #f5f5f5;
      }

      .container {
        max-width: 30rem;
        min-width: 20rem;
        margin: 0 auto;
        background-color: white;
        min-height: 100vh;
      }

      .header-search {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background: linear-gradient(135deg, #8b5cf6, #a855f7);
      }

      .header-search input {
        flex: 1;
        padding: 0.75rem 1rem;
        border: none;
        border-radius: 1.5rem 0 0 1.5rem;
        font-size: 0.9rem;
        outline: none;
        background-color: rgba(255, 255, 255, 0.9);
        color: #333;
      }

      .header-search input::placeholder {
        color: #999;
        font-size: 0.85rem;
      }

      .header-search button {
        padding: 0.75rem 1.5rem;
        background-color: #7c3aed;
        color: white;
        border: none;
        border-radius: 0 1.5rem 1.5rem 0;
        font-size: 0.9rem;
        cursor: pointer;
        white-space: nowrap;
        transition: background-color 0.2s;
      }

      .header-search button:hover {
        background-color: #6d28d9;
      }

      .main {
        padding: 1rem;
      }

      .main-one {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        line-height: 1.5;
        color: #666;
      }

      .main-two {
        text-align: center;
        margin-bottom: 1rem;
      }

      .main-two img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
      }

      .footer ul {
        list-style: none;
        display: flex;
        justify-content: center;
        gap: 1rem;
        padding: 1rem;
      }

      .footer li {
        width: 0.5rem;
        height: 0.5rem;
        background-color: #ddd;
      }
    </style>
  </head>
  <!--宽度最大480px，最小320px-->
  <body>
    <div class="container">
      <div class="header-search">
        <svg
          t="1752904052065"
          class="icon"
          viewBox="0 0 1024 1024"
          version="1.1"
          xmlns="http://www.w3.org/2000/svg"
          p-id="3133"
          width="32"
          height="32"
        >
          <path
            d="M415.59889935 818.40673751c-103.69194019 0-207.38388041-39.48836529-286.31642412-118.42090898-157.86508737-157.87981634-157.86508737-414.78248979 0-572.66230613 157.85035841-157.85035841 414.76776082-157.90927428 572.64757719 0 157.86508737 157.87981634 157.86508737 414.78248979 0 572.66230613-78.93254368 78.93254368-182.63921287 118.42090898-286.33115307 118.42090898z m0-725.22496474c-82.09927197 0-164.21327292 31.25487175-226.70828746 93.74988629-125.00475803 125.00475803-125.00475803 328.44127481 0 453.44603281 125.01948701 124.9753001 328.41181686 125.03421596 453.43130386 0 125.00475803-125.00475803 125.00475803-328.44127481 0-453.44603281-62.5097435-62.49501453-144.60901547-93.74988627-226.7230164-93.74988629z"
            fill="#515151"
            p-id="3134"
          ></path>
          <path
            d="M973.48804978 1013.69813456c-10.78160515 0-21.57793927-4.10938229-29.79670383-12.34287584L658.31757585 715.95203069c-16.46698708-16.46698708-16.46698708-43.14114955 0-59.60813666s43.14114955-16.46698708 59.60813665 0l285.37377009 285.38849908c16.46698708 16.46698708 16.46698708 43.14114955 0 59.60813663a42.07329932 42.07329932 0 0 1-29.81143281 12.35760482z"
            fill="#515151"
            p-id="3135"
          ></path>
        </svg>
        <input type="text" placeholder="请输入您的商家订单号" />
        <button>查询</button>
      </div>
      <div class="main">
        <div class="main-one">
          微信支付:点击微信列表的「微信支付」进入交易记录，复制商户单号到当前页面进行查询
        </div>
        <div class="main-two">
          <img src="image.png" />
        </div>
      </div>
      <div class="footer">
        <ul>
          <li>首页</li>
          <li>个人中心</li>
        </ul>
      </div>
    </div>
  </body>
</html>
