<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>practice 4</title>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: Arial, sans-serif;
        background-color: #f5f5f5;
      }

      .container {
        max-width: 30rem; 
        min-width: 20rem;
        margin: 0 auto;
        background-color: white;
        min-height: 100vh;
      }

      .header-search {
        display: flex;
        align-items: center;
        padding: 0.75rem;
        background: linear-gradient(135deg, #8b5cf6, #a855f7);
        gap: 0.5rem;
      }

      .header-search input {
        flex: 1;
        padding: 0.75rem 1rem;
        border: none;
        border-radius: 1.5rem;
        font-size: 0.9rem;
        outline: none;
        background-color: rgba(255, 255, 255, 0.9);
        color: #333;
      }

      .header-search input::placeholder {
        color: #999;
        font-size: 0.85rem;
      }

      .header-search button {
        padding: 0.75rem 1.5rem;
        background-color: #7c3aed;
        color: white;
        border: none;
        border-radius: 1.5rem;
        font-size: 0.9rem;
        cursor: pointer;
        white-space: nowrap;
        transition: background-color 0.2s;
      }

      .header-search button:hover {
        background-color: #6d28d9;
      }

      .main {
        padding: 1rem;
      }

      .main-one {
        background-color: #f8f9fa;
        padding: 1rem;
        border-radius: 0.5rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        line-height: 1.5;
        color: #666;
      }

      .main-two {
        text-align: center;
        margin-bottom: 1rem;
      }

      .main-two img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
      }

      .footer ul {
        list-style: none;
        display: flex;
        justify-content: center;
        gap: 1rem;
        padding: 1rem;
      }

      .footer li {
        width: 0.5rem;
        height: 0.5rem;
        background-color: #ddd;
      }
    </style>
  </head>
  <!--宽度最大480px，最小320px-->
  <body>
    <div class="container">
      <div class="header-search">
        <input type="text" placeholder="请输入您的商家订单号" />
        <button>查询</button>
      </div>
      <div class="main">
        <div class="main-one">
          微信支付:点击微信列表的「微信支付」进入交易记录，复制商户单号到当前页面进行查询
        </div>
        <div class="main-two">
          <img src="image.png" />
        </div>
      </div>
      <div class="footer">
        <ul>
          <li>首页</li>
          <li>个人中心</li>
        </ul>
      </div>
    </div>
  </body>
</html>
