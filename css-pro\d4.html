<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>practice 4</title>
    <!--宽度最大480px，最小320px，但是在大于480px的狂赌也可以显示一样的效果-->
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }
      body {
        background-color: #cab2f1;
        padding-top: 5rem;
      }
      .container {
        max-width: 30rem; 
        min-width: 20rem; 
        margin: 0 auto;
        background-color: white;
        min-height: 100vh;
      }

      .header-search {
        position: fixed;
        top: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100%;
        display: flex;
        align-items: center;
        padding: 0.75rem;
        z-index: 1000;
      }

      .search-input-container {
        flex: 1;
        position: relative;
        display: flex;
        align-items: center;
      }

      .header-search input {
        width: 100%;
        padding: 0.75rem 1rem 0.75rem 2.5rem;
        border: none;
        border-radius: 1.5rem 0 0 1.5rem;
        font-size: 0.9rem;
        outline: none;
        background-color: rgba(255, 255, 255, 0.9);
        color: #333;
      }

      .search-icon {
        position: absolute;
        left: 0.75rem;
        width: 1.2rem;
        height: 1.2rem;
        color: #666;
        pointer-events: none;
        z-index: 1;
      }

      .header-search input::placeholder {
        color: #999;
        font-size: 0.85rem;
      }

      .header-search button {
        padding: 0.75rem 1.5rem;
        background-color: #7c3aed;
        color: white;
        border: none;
        border-radius: 0 1.5rem 1.5rem 0;
        font-size: 0.9rem;
        cursor: pointer;
        white-space: nowrap;
        transition: background-color 0.2s;
      }

      .header-search button:hover {
        background-color: #6d28d9;
      }

      .main {
        padding: 1rem;
      }

      .main-one {
        padding: 1rem;
        margin-bottom: 1rem;
        font-size: 0.9rem;
        line-height: 1.5;
        color: #666;
      }

      .main-two {
        text-align: center;
        margin-bottom: 1rem;
      }

      .main-two img {
        max-width: 100%;
        height: auto;
        border-radius: 0.5rem;
      }
      .footer {
        position: fixed;
        bottom: 0;
        padding: 1rem;
        width: 100%;
        background-color: #f8f9fa;
        border-top: 1px solid #eee;
      }
      .footer ul {
        list-style: none;
        display: flex;
        justify-content: space-evenly;
        gap: 1rem;
      }
      .footer li {
        white-space: nowrap;
      }
      .footer li div {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
      }
      .footer-icon {
        left: 0.75rem;
        width: 1.2rem;
        height: 1.2rem;
        color: gray;
        pointer-events: none;
        padding-bottom: 0.0133em;
      }
    </style>
  </head>

  <body>
    <div class="container">
      <div class="header-search">
        <div class="search-input-container">
          <svg
            class="search-icon"
            viewBox="0 0 1024 1024"
            version="1.1"
            xmlns="http://www.w3.org/2000/svg"
          >
            <path
              d="M415.59889935 818.40673751c-103.69194019 0-207.38388041-39.48836529-286.31642412-118.42090898-157.86508737-157.87981634-157.86508737-414.78248979 0-572.66230613 157.85035841-157.85035841 414.76776082-157.90927428 572.64757719 0 157.86508737 157.87981634 157.86508737 414.78248979 0 572.66230613-78.93254368 78.93254368-182.63921287 118.42090898-286.33115307 118.42090898z m0-725.22496474c-82.09927197 0-164.21327292 31.25487175-226.70828746 93.74988629-125.00475803 125.00475803-125.00475803 328.44127481 0 453.44603281 125.01948701 124.9753001 328.41181686 125.03421596 453.43130386 0 125.00475803-125.00475803 125.00475803-328.44127481 0-453.44603281-62.5097435-62.49501453-144.60901547-93.74988627-226.7230164-93.74988629z"
              fill="currentColor"
            ></path>
            <path
              d="M973.48804978 1013.69813456c-10.78160515 0-21.57793927-4.10938229-29.79670383-12.34287584L658.31757585 715.95203069c-16.46698708-16.46698708-16.46698708-43.14114955 0-59.60813666s43.14114955-16.46698708 59.60813665 0l285.37377009 285.38849908c16.46698708 16.46698708 16.46698708 43.14114955 0 59.60813663a42.07329932 42.07329932 0 0 1-29.81143281 12.35760482z"
              fill="currentColor"
            ></path>
          </svg>
          <input type="text" placeholder="请输入您的商家订单号" />
        </div>
        <button>查询</button>
      </div>
      <div class="main">
        <div class="main-one">
          微信支付:点击微信列表的「微信支付」进入交易记录，复制商户单号到当前页面进行查询
        </div>
        <div class="main-two">
          <img src="./image/image1.png" />
        </div>
        <div class="main-one">
          支付宝支付:点击支付宝列表的[账单]进入交易记录，复制商户单号到当前页面进行查询
        </div>
        <div class="main-two">
          <img src="./image/image.png" />
        </div>
      </div>
      <div class="footer">
        <ul>
          <li>
            <div>
              <svg
                class="footer-icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M234.688 944h554.624q33.152 0 56.576-23.424t23.424-56.576V529.92h51.776q38.528 0 52.288-36.032 13.76-36.032-15.04-61.696L568.704 84.416q-22.656-20.16-52.928-20.288-30.336-0.128-53.12 19.84L66.688 431.936q-29.056 25.6-15.36 61.824 13.632 36.288 52.352 36.288h51.008V864q0 33.152 23.424 56.576t56.576 23.424z m405.312-64V704q0-33.152-23.424-56.576T560 624h-96q-33.152 0-56.576 23.424T384 704v176H234.688q-6.656 0-11.328-4.672t-4.672-11.328V505.984q0-40-40-40h-53.76l379.968-333.888q10.624-9.344 21.184 0.064l374.016 333.824h-54.784q-40 0-40 40V864q0 16-16 16H640zM576 704v176H448V704q0-6.656 4.672-11.328t11.328-4.672h96q6.656 0 11.328 4.672T576 704z"
                  fill="#303133"
                  p-id="6069"
                ></path>
              </svg>
              <p>首页</p>
            </div>
          </li>
          <li>
            <div>
              <svg
                class="footer-icon"
                viewBox="0 0 1024 1024"
                version="1.1"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  d="M512 1024C230.4 1024 0 793.6 0 512S230.4 0 512 0s512 230.4 512 512-230.4 512-512 512z m0-921.6c-225.28 0-409.6 184.32-409.6 409.6s184.32 409.6 409.6 409.6 409.6-184.32 409.6-409.6-184.32-409.6-409.6-409.6z"
                  fill="#333333"
                  p-id="7296"
                ></path>
                <path
                  d="M512 849.92c-128 0-245.76-76.8-302.08-194.56l92.16-40.96c40.96 81.92 122.88 133.12 209.92 133.12 87.04 0 174.08-51.2 209.92-133.12l92.16 46.08c-56.32 112.64-174.08 189.44-302.08 189.44z"
                  fill="#333333"
                  p-id="7297"
                ></path>
              </svg>
              <p>个人中心</p>
            </div>
          </li>
        </ul>
      </div>
    </div>
  </body>
</html>
