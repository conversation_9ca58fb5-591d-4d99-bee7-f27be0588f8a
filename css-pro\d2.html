<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>practice1</title>
  </head>
  <!--
  左侧导航宽度固定200px，上方固定80px，
  右侧可随屏幕宽度变化而变化，当屏幕宽度小于800px时，
  不再随着屏幕减小而是内容区出现水平滚动条。

  左侧笔试名称，技术方向和查询重置左对齐，右侧两按钮右对齐，
  当红框区域宽度小于800px时，不随着屏幕宽度变小而变小
  -->
  <style>
    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      background-color: #12981f;
    }
    .container .header {
      height: 80px;
      background-color: #280fb2;
      border-bottom: 1px solid #ccc;
    }
    .main {
      display: flex;
      flex: 1;
      background-color: aqua;
    }
    .main .nav {
      width: 200px;
      background-color: #c61515;
      border-right: 1px solid #9fbd1d;
    }
    .main .content {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      flex: 1;
    }
    .content .content-search {
      display: flex;
      justify-content: space-around;
      background-color: #7907a2;
      border-right: 1px solid #ccc;
    }
    .content .content-search .search1 {
      width: 100px;
      height: 50px;
      background-color: #000000;
    }
    .content .content-search .search2 {
      width: 100px;
      height: 50px;
      background-color: #000000;
    }
    .content .content-search .search-btns {
      display: flex;

    .content .content-show {
      flex: 1;
      background-color: #000000;
      border-right: 1px solid #ccc;
    }
    @media (max-width: 800px) {
      .container {
        width: 800px;
      }
      .main .content {
        width: 800px;
        overflow-x: auto;
      }
    }
  </style>
  <body>
    <div class="container">
      <div class="header"></div>
      <div class="main">
        <div class="nav"></div>
        <div class="content">
          <div class="content-search">
            <div class="search1"></div>
            <div class="search2"></div>
            <div class="search-btns">
              <button>查询</button>
              <button>重置</button>
            </div>
            <div class="add-btns">
              <button>+智能一键组卷</button>
              <button>+创建笔试</button>
            </div>
          </div>
          <div class="content-show"></div>
        </div>
      </div>
    </div>
  </body>
</html>
