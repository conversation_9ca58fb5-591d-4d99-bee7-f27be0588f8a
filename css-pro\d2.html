<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>practice2</title>
  </head>
  <!--
  左侧导航宽度固定200px，上方固定80px，
  右侧可随屏幕宽度变化而变化，当屏幕宽度小于800px时，
  不再随着屏幕减小而是内容区出现水平滚动条。

  左侧笔试名称，技术方向和查询重置左对齐，右侧两按钮右对齐，
  当红框区域宽度小于800px时，不随着屏幕宽度变小而变小
  -->
  <style>
    .container {
      display: flex;
      flex-direction: column;
      height: 100vh;
      background-color: #12981f;
    }
    .container .header {
      height: 80px;
      background-color: #280fb2;
      border-bottom: 1px solid #ccc;
    }
    .main {
      display: flex;
      flex: 1;
      background-color: aqua;
    }
    .main .nav {
      width: 200px;
      background-color: #c61515;
      border-right: 1px solid #9fbd1d;
    }
    .main .content {
      display: flex;
      flex-direction: column;
      justify-content: space-around;
      flex: 1;
    }
    .content .content-search {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-color: #7907a2;
      border-right: 1px solid #ccc;
      padding: 10px 20px;
    }

    .content-search .left-section {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .content-search .right-section {
      display: flex;
      align-items: center;
      gap: 10px;
    }

    .search1,
    .search2 {
      padding: 5px 10px;
      background-color: #fff;
      border: 1px solid #ccc;
      border-radius: 4px;
      min-width: 120px;
      height: 30px;
    }

    .search-btns {
      display: flex;
      gap: 10px;
    }

    .search-btns button,
    .add-btns button {
      padding: 8px 16px;
      border: none;
      border-radius: 4px;
      cursor: pointer;
    }

    .search-btns button {
      background-color: #4caf50;
      color: white;
      min-width: 60px;
    }

    .add-btns {
      display: flex;
      gap: 10px;
    }

    .add-btns button {
      background-color: #2196f3;
      color: white;
      min-width: 120px;
    }
    .content .content-show {
      flex: 1;
      background-color: #000000;
      border-right: 1px solid #ccc;
    }
    @media (max-width: 800px) {
      .container {
        width: 800px;
      }
      .main .content {
        width: 800px;
        overflow-x: auto;
      }
      .content-search {
        width: 800px;
      }
    }
  </style>
  <body>
    <div class="container">
      <div class="header"></div>
      <div class="main">
        <div class="nav"></div>
        <div class="content">
          <div class="content-search">
            <div class="left-section">
              <div class="search1">笔试名称</div>
              <div class="search2">技术方向</div>
              <div class="search-btns">
                <button>查询</button>
                <button>重置</button>
              </div>
            </div>
            <div class="right-section">
              <div class="add-btns">
                <button>+智能一键组卷</button>
                <button>+创建笔试</button>
              </div>
            </div>
          </div>
          <div class="content-show"></div>
        </div>
      </div>
    </div>
  </body>
</html>
