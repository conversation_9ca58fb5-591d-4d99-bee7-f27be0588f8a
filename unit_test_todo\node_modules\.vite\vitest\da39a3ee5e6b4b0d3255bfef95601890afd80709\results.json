{"version": "3.2.4", "results": [[":src/react/utils/todoUtils.test.js", {"duration": 0, "failed": true}], [":src/react/test-components/TodoInput.test.tsx", {"duration": 5.5644000000320375, "failed": true}], [":src/react/_test_/TodoInput.test.tsx", {"duration": 0, "failed": true}], [":src/react/_test_/todoUtils.test.js", {"duration": 23.12630000000013, "failed": false}], [":src/react/_test_/TodoApp.test.jsx", {"duration": 103.0273000000002, "failed": true}], [":src/react/_test_/useTodoStore.test.jsx", {"duration": 33.55639999999994, "failed": false}], [":src/react/_test_/TodoItem.test.jsx", {"duration": 0, "failed": false}], [":src/react/_test_/TodoInput.test.jsx", {"duration": 0, "failed": false}], [":src/react/_test_/TodoStats.test.jsx", {"duration": 0, "failed": false}]]}