{"version": "3.2.4", "results": [[":src/react/utils/todoUtils.test.js", {"duration": 0, "failed": true}], [":src/react/test-components/TodoInput.test.tsx", {"duration": 5.5644000000320375, "failed": true}], [":src/react/_test_/TodoInput.test.tsx", {"duration": 0, "failed": true}], [":src/react/_test_/todoUtils.test.js", {"duration": 29.653600000000097, "failed": false}], [":src/react/_test_/TodoApp.test.jsx", {"duration": 6.182900000000245, "failed": true}], [":src/react/_test_/useTodoStore.test.jsx", {"duration": 30.375999999999976, "failed": false}], [":src/react/_test_/TodoItem.test.jsx", {"duration": 126.80150000000003, "failed": false}], [":src/react/_test_/TodoInput.test.jsx", {"duration": 133.8795, "failed": false}], [":src/react/_test_/TodoStats.test.jsx", {"duration": 103.6586000000002, "failed": false}]]}