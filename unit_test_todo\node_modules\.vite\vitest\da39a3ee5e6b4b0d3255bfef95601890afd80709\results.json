{"version": "3.2.4", "results": [[":src/react/utils/todoUtils.test.js", {"duration": 0, "failed": true}], [":src/react/test-components/TodoInput.test.tsx", {"duration": 5.5644000000320375, "failed": true}], [":src/react/_test_/TodoInput.test.tsx", {"duration": 0, "failed": true}], [":src/react/_test_/todoUtils.test.js", {"duration": 29.79370000000017, "failed": false}], [":src/react/_test_/TodoApp.test.jsx", {"duration": 168.5021999999999, "failed": false}], [":src/react/_test_/useTodoStore.test.jsx", {"duration": 35.81230000000005, "failed": false}], [":src/react/_test_/TodoItem.test.jsx", {"duration": 130.0037000000002, "failed": false}], [":src/react/_test_/TodoInput.test.jsx", {"duration": 199.45620000000008, "failed": false}], [":src/react/_test_/TodoStats.test.jsx", {"duration": 98.60609999999997, "failed": false}]]}